ALTER PROC dbo.tr_exportGLAccountsStructure
@orgID int,
@exportPath varchar(400)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @orgCode varchar(10), @cmd varchar(4000), @svrName varchar(40), @ApplicationCreatedContentSRTID int, @GLAccountsAdminSRTID int;
	DECLARE @tblGLAA TABLE (siteID int, siteResourceID int PRIMARY KEY);
	SET @svrName = CAST(SERVERPROPERTY('ServerName') AS varchar(40));

	SELECT @orgCode = orgCode FROM dbo.organizations WHERE orgID = @orgID;
	SELECT @ApplicationCreatedContentSRTID = resourceTypeID FROM dbo.cms_siteResourceTypes WHERE resourceType = 'ApplicationCreatedContent';
	SELECT @GLAccountsAdminSRTID = resourceTypeID FROM dbo.cms_siteResourceTypes WHERE resourceType = 'GLAccountsAdmin';

	DELETE FROM datatransfer.dbo.sync_tr_GLAccounts WHERE orgCode = @orgCode;
	DELETE FROM datatransfer.dbo.sync_tr_gl_content WHERE orgCode = @orgCode;
	DELETE FROM datatransfer.dbo.sync_tr_gl_supporting WHERE orgCode = @orgCode;

	INSERT INTO datatransfer.dbo.sync_tr_GLAccounts (orgCode, orgID, GLAccountID, [uid], AccountTypeID, AccountName, AccountCode, 
		parentGLAccountID, [status], isSystemAccount, GLCode, invoiceProfileID, invoiceContentID, deferredGLAccountID, 
		salesTaxProfileID, salesTaxTaxJarCategoryID, acctSysName, acctSysClass, thePathExpanded)
	select @orgCode, @orgID, account.GLAccountID, gl.uid, account.accounttypeid, account.accountname, isnull(account.accountcode,''), 
		isnull(account.parentglaccountid,0) as parentglaccountid, account.status, account.issystemaccount, isnull(account.glcode,''), 
		isnull(account.invoiceprofileid,0), isnull(gl.invoiceContentID,0), isnull(account.deferredglaccountid,0), 
		isnull(gl.salesTaxProfileID,0), isnull(gl.salesTaxTaxJarCategoryID,0), isnull(gl.acctSysName,''), isnull(gl.acctSysClass,''), 
		account.thepathexpanded
	from dbo.fn_getRecursiveGLAccountsWithAccountTypes(@orgID) as account
	inner join dbo.tr_GlAccounts as gl on gl.glAccountID = account.glAccountID
	where account.GLAccountID > 0
	and account.status <> 'D'
	and (account.isSystemAccount = 0 OR (account.accounttypeid = 5 AND isnull(account.glcode,'') <> 'DEPOSITS'))
	order by account.thePath;

	INSERT INTO @tblGLAA (siteID, siteResourceID)
	SELECT s.siteID, sr.siteResourceID
	FROM dbo.sites as s
	INNER JOIN dbo.cms_siteResources as sr on sr.siteID = s.siteID
		and sr.siteResourceStatusID = 1
		and sr.resourceTypeID = @GLAccountsAdminSRTID
	WHERE s.orgID = @orgID;

	INSERT INTO datatransfer.dbo.sync_tr_gl_content (orgCode, orgID, contentID, contentTitle, rawContent)
	SELECT @orgCode, @orgID, c.contentID, glContent.contentTitle, glContent.rawContent
	FROM @tblGLAA as sr
	INNER JOIN dbo.cms_siteResources as sr2 on sr2.siteID = sr.siteID
		and sr2.siteResourceStatusID = 1
		and sr2.resourceTypeID = @ApplicationCreatedContentSRTID
		and sr2.parentSiteResourceID = sr.siteResourceID
	INNER JOIN dbo.cms_content as c on c.siteID = sr2.siteID
		and c.siteResourceID = sr2.siteResourceID
	CROSS APPLY dbo.fn_getContent(c.contentID,1) as glContent;

	INSERT INTO datatransfer.dbo.sync_tr_gl_supporting (orgCode, orgID, cat, itemID, itemType)
	select distinct @orgCode, @orgID, 'acctip', gl.invoiceProfileID, ip.profileName
	from dbo.tr_GlAccounts as gl
	inner join dbo.tr_invoiceProfiles as ip on ip.orgID = @orgID and ip.profileID = gl.invoiceProfileID
	where gl.orgID = @orgID;

	INSERT INTO datatransfer.dbo.sync_tr_gl_supporting (orgCode, orgID, cat, itemID, itemType, itemType2)
	select distinct @orgCode, @orgID, 'salestxp', tp.profileID, tp.profileName, stp.providerName
	from dbo.tr_salesTaxProfiles as tp
	inner join dbo.tr_GlAccounts as gl on gl.orgID = @orgID and gl.salesTaxProfileID = tp.profileID
	inner join dbo.tr_salesTaxProviders as stp on stp.providerID = tp.providerID
	where tp.orgID = @orgID;

	INSERT INTO datatransfer.dbo.sync_tr_gl_supporting (orgCode, orgID, cat, itemID, itemType, itemType2)
	select distinct @orgCode, @orgID, 'taxjarcat', tcat.categoryID, tcat.category, tcat.taxCode
	from dbo.tr_salesTaxCategories_TaxJar as tcat
	inner join dbo.tr_GlAccounts as gl on gl.orgID = @orgID and gl.salesTaxTaxJarCategoryID = tcat.categoryID;
	
	-- export to file
	SET @cmd = 'bcp "SELECT orgCode, CAST(NULL AS int) AS orgID, GLAccountID, [uid], AccountTypeID, AccountName, AccountCode, parentGLAccountID, [status], isSystemAccount, GLCode, invoiceProfileID, invoiceContentID, deferredGLAccountID, salesTaxProfileID, salesTaxTaxJarCategoryID, acctSysName, acctSysClass, thePathExpanded, finalAction FROM datatransfer.dbo.sync_tr_GLAccounts WHERE orgCode = ''' + @orgCode + '''" queryout "'+@exportPath+'sync_tr_GLAccounts.bcp" -t'+CHAR(7)+' -w -T -S' + @svrName;
	EXEC master..xp_cmdshell @cmd, NO_OUTPUT;

	SET @cmd = 'bcp "SELECT orgCode, CAST(NULL AS int) AS orgID, contentID, contentTitle, rawContent, useContentID, finalAction FROM datatransfer.dbo.sync_tr_gl_content WHERE orgCode = ''' + @orgCode + '''" queryout "'+@exportPath+'sync_tr_gl_content.bcp" -t'+CHAR(7)+' -w -T -S' + @svrName;
	EXEC master..xp_cmdshell @cmd, NO_OUTPUT;

	SET @cmd = 'bcp "SELECT orgCode, CAST(NULL AS int) AS orgID, cat, itemID, itemID2, itemID3, itemID4, itemType, itemType2, itemUID, useID FROM datatransfer.dbo.sync_tr_gl_supporting WHERE orgCode = ''' + @orgCode + '''" queryout "'+@exportPath+'sync_tr_gl_supporting.bcp" -t'+CHAR(7)+' -w -T -S' + @svrName;
	EXEC master..xp_cmdshell @cmd, NO_OUTPUT;

	-- clear sync tables
	DELETE FROM datatransfer.dbo.sync_tr_GLAccounts WHERE orgID = @orgID;
	DELETE FROM datatransfer.dbo.sync_tr_gl_content WHERE orgID = @orgID;
	DELETE FROM datatransfer.dbo.sync_tr_gl_supporting WHERE orgID = @orgID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
