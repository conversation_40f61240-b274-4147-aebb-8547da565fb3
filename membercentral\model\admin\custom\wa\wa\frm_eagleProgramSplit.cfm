<cfsavecontent variable="local.programSplitJS">
	<cfoutput>
	<script language="javascript">
	function validateProgSplitFrm() {
		var arrReq = new Array();
		mca_hideAlert('err_programsplit');

		if($('##effectiveDate').val() == '')
			arrReq[arrReq.length] = 'Enter the effective date.';
		if($('##splitGroupID').val() == '')
			arrReq[arrReq.length] = 'Select a group.';
		if($('##GLAccountID').val() == 0)
			arrReq[arrReq.length] = 'Select a GL Account.';
		if (parseInt($('##WSAJPct').val()) < 0 || parseInt($('##WSAJPct').val()) > 100)
			arrReq[arrReq.length] = 'Enter a valid WSAJ Percentage.';
		if (parseInt($('##PACPct').val()) < 0 || parseInt($('##PACPct').val()) > 100)
			arrReq[arrReq.length] = 'Enter a valid PAC Percentage.';
		if (parseInt($('##ActionPct').val()) < 0 || parseInt($('##ActionPct').val()) > 100)
			arrReq[arrReq.length] = 'Enter a valid Action Percentage.';
		
		var totalPct = 0;
		var WSAJPct = !isNaN(parseInt(Number($('##WSAJPct').val()))) ? parseInt(Number($('##WSAJPct').val())) : 0;
		var PACPct = !isNaN(parseInt(Number($('##PACPct').val()))) ? parseInt(Number($('##PACPct').val())) : 0;
		var ActionPct = !isNaN(parseInt(Number($('##ActionPct').val()))) ? parseInt(Number($('##ActionPct').val())) : 0;
		totalPct = WSAJPct + PACPct + ActionPct;

		if(totalPct != 100) 
			arrReq[arrReq.length] = 'Total Split Percentage must add to 100.';

		if(arrReq.length){
			mca_showAlert('err_programsplit', arrReq.join('<br/>'));
			return false;
		}

		$('##btnSave').attr('disabled', true);
		$('##frmProgSplit').submit();
	}
	function cleanPct(el) {
		el.val(parseInt(0+el.val()));
	}
	<!--- gl account selector functions --->
	function selectGLAccount() {
		$('##divProgSplitForm').hide();
		toggleGLASelectorGridArea(true);
	}
	function selectGLAccountResult(objGL) {
		if (objGL.thepathexpanded.length > 0) {
			$('##GLAccountPath').html('<span class="mr-1">' + objGL.thepathexpanded + '</span> (<span class="text-danger font-weight-bold">Remember to save!</span>)');
			$('##GLAccountID').val(objGL.glaccountid);
		} else { 
			var msg = 'There was a problem selecting the GL Account for this program split.<br/>
				Try again; if the issue persists, contact MemberCentral for assistance.';
			$('##divGLerr').html(msg).show();
		}
		$('##divProgSplitForm').show();
		toggleGLASelectorGridArea(false,true);
	}
	$(function(){
		mca_setupDatePickerField('effectiveDate');
		mca_setupCalendarIcons('frmProgSplit');
	});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.programSplitJS)#">

<cfoutput>
<div class="mb-3 p-3" id="divProgSplitForm">
	<form name="frmProgSplit" id="frmProgSplit" action="#local.formlink#" method="post">
		<input type="hidden" name="splitID" id="programID" value="#local.splitID#">

		<div id="err_programsplit" class="alert alert-danger mb-2 d-none"></div>
		<div class="form-group row no-gutters align-items-center">
			<label for="effectiveDate" class="col-4 col-form-label">Effective Date *</label>
			<div class="col-8 d-flex">
				<div class="input-group input-group-sm">
					<input class="form-control form-control-sm dateControl" type="text" name="effectiveDate" id="effectiveDate" value="#DateFormat(arguments.event.getValue('effectiveDate'),"m/d/yyyy")#" autocomplete="off">
					<div class="input-group-append">
						<span class="input-group-text cursor-pointer calendar-button" data-target="effectiveDate"><i class="fa-solid fa-calendar"></i></span>
					</div>
				</div>
				<button type="button" class="btnClearDateCF btn btn-pill btn-secondary btn-sm ml-2 py-0" name="btnClearDate" onclick="mca_clearDateRangeField('effectiveDate')">clear</button>
			</div>
		</div>
		<div class="form-group row no-gutters align-items-center">
			<label for="splitGroupID" class="col-4 col-form-label">Group *</label>
			<div class="col-8">
				<select class="form-control form-control-sm" name="splitGroupID" id="splitGroupID">
					<option value="">Select a group</option>
					<cfloop query="local.qryGroups">
						<option value="#local.qryGroups.groupID#" <cfif arguments.event.getValue('groupID') eq local.qryGroups.groupID>selected="selected"</cfif>><cfloop from="1" to="#listLen(local.qryGroups.thePath,".")#" index="x">&nbsp;&nbsp;&nbsp;</cfloop>#local.qryGroups.groupName#</option>
					</cfloop>
				</select>
			</div>
		</div>
		<div class="form-group row no-gutters">
			<label for="columnName" class="col-sm-4 col-form-label">
				Revenue GL *
			</label>
			<div class="col-sm-8">
				<input type="hidden" name="GLAccountID" id="GLAccountID" value="#val(arguments.event.getValue('GLAccountID'))#">
				<span id="GLAccountPath"><cfif len(arguments.event.getValue('GLAccountPath'))>#arguments.event.getValue('GLAccountPath')#<cfelse>(No account selected)</cfif></span>
				<br/><a href="javascript:selectGLAccount();">Choose GL Account</a>
				<div id="divGLerr" class="alert alert-danger" style="display:none;"></div>
			</div>
		</div>
		<div class="form-group row no-gutters">
			<label for="WSAJPct" class="col-sm-4 col-form-label">
				WSAJ Percentage
			</label>
			<div class="col-sm-auto d-inline-flex">
				<input class="form-control form-control-sm text-right" type="text" name="WSAJPct" id="WSAJPct" value="#arguments.event.getValue('WSAJPct')#" maxlength="3"  onBlur="cleanPct($(this));"> <span class="ml-1">%</span>
			</div>
		</div>
		<div class="form-group row no-gutters">
			<label for="PACPct" class="col-sm-4 col-form-label">
				PAC Percentage
			</label>
			<div class="col-sm-auto d-inline-flex">
				<input class="form-control form-control-sm text-right" type="text" name="PACPct" id="PACPct" value="#arguments.event.getValue('PACPct')#" maxlength="3" onBlur="cleanPct($(this));"><span class="ml-1">%</span>
			</div>
		</div>
		<div class="form-group row no-gutters">
			<label for="ActionPct" class="col-sm-4 col-form-label">
				Action Percentage
			</label>
			<div class="col-sm-auto d-inline-flex">
				<input class="form-control form-control-sm text-right" type="text" name="ActionPct" id="ActionPct" value="#arguments.event.getValue('ActionPct')#" maxlength="3" onBlur="cleanPct($(this));"><span class="ml-1">%</span>
			</div>
		</div>
	</form>
</div>
<div class="p-3">#local.showGLSelector.data#</div>
</cfoutput>