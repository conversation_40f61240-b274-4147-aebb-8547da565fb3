<cfcomponent extends="model.admin.admin" output="no">
	<cfset defaultEvent = 'controller'>

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			// build quick links
			this.link.listSplits = buildCurrentLink(arguments.event,"listSplits");
			this.link.editProgramSplit = buildCurrentLink(arguments.event,"editProgramSplit") & "&mode=direct";
			this.link.saveProgramSplit = buildCurrentLink(arguments.event,"saveProgramSplit") & "&mode=stream";
			
			// method to run
			local.methodToRun = this[arguments.event.getValue('mca_ta')];
			// pass the argument collection to the current method and execute it.
			return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>		
	
	<cffunction name="listSplits" access="public" output="false" returntype="struct" hint="Eagle Splits List Page">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.splitList = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=eagleProgramSplitJSON&meth=getlistSplits&mode=stream&srID=#this.siteResourceID#";
		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_eagleProgramSplit.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>

	<cffunction name="getEagleProgramSplit" access="private" output="false" returntype="query">
		<cfargument name="splitID" type="numeric" required="yes">

		<cfset var qryEagleProgramSplit = "">

		<cfquery name="qryEagleProgramSplit" datasource="#application.dsn.customapps.dsn#">
			select splitID, groupID, GLAccountID, WSAJPct, PACPct, ActionPct, effectiveDate
			from dbo.WA_EagleProgramSplits
			where splitID = <cfqueryparam value="#arguments.splitID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>

		<cfreturn qryEagleProgramSplit>
	</cffunction>

	<cffunction name="editProgramSplit" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.formLink = this.link.saveProgramSplit;
		local.splitID = arguments.event.getValue('sID',0);

		local.qryEagleProgramSplit = getEagleProgramSplit(splitID=local.splitID);
		local.qryGroups = CreateObject("component","model.admin.groups.groups").getGroups(orgID=arguments.event.getValue('mc_siteInfo.orgID'),excludeGroupID='Default',hideProtected=1);

		arguments.event.setValue('groupID',local.qryEagleProgramSplit.groupID);
		arguments.event.setValue('WSAJPct',local.qryEagleProgramSplit.WSAJPct);
		arguments.event.setValue('PACPct',local.qryEagleProgramSplit.PACPct);
		arguments.event.setValue('ActionPct',local.qryEagleProgramSplit.ActionPct);
		arguments.event.setValue('effectiveDate',local.qryEagleProgramSplit.effectiveDate);
		arguments.event.setValue('glAccountID',val(local.qryEagleProgramSplit.glAccountID));
		local.tmpStrAccount = CreateObject("component","model.admin.GLAccounts.GLAccounts").getGLAccount(GLAccountID=val(local.qryEagleProgramSplit.glAccountID), orgID=arguments.event.getValue('mc_siteInfo.orgID'));
		arguments.event.setValue('GLAccountPath',local.tmpStrAccount.qryAccount.thePathExpanded);

		// set account type to revenue
		arguments.event.setValue('glatid',3);
		arguments.event.setValue('selectFN','selectGLAccountResult');
		local.showGLSelector = CreateObject("component","model.admin.GLAccounts.GLAccountsAdmin").showSelector(event=arguments.event, appInstanceID=this.appInstanceID);
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_eagleProgramSplit.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveProgramSplit" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();

		if (arguments.event.getValue('splitID',0) gt 0) {
			updateProgramSplit(splitID=arguments.event.getValue('splitID'), groupID=arguments.event.getValue('splitGroupID',0),
					glAccountID=val(arguments.event.getValue('glAccountID',0)), WSAJPct=val(arguments.event.getValue('WSAJPct',0)),
					PACPct=val(arguments.event.getValue('PACPct',0)), ActionPct=val(arguments.event.getValue('ActionPct',0)),
					effectiveDate=arguments.event.getValue('effectiveDate',''));
		} else {
			local.splitID = insertProgramSplit(groupID=arguments.event.getValue('splitGroupID',0), glAccountID=val(arguments.event.getValue('glAccountID',0)), 
					WSAJPct=val(arguments.event.getValue('WSAJPct',0)), PACPct=val(arguments.event.getValue('PACPct',0)), 
					ActionPct=val(arguments.event.getValue('ActionPct',0)), effectiveDate=arguments.event.getValue('effectiveDate',''));
		}
		</cfscript>	

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.reloadProgramSplitTable();
				top.MCModalUtils.hideModal();
			</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="insertProgramSplit" access="private" output="false" returntype="numeric">
		<cfargument name="groupID" type="numeric" required="yes">
		<cfargument name="glAccountID" type="numeric" required="yes">
		<cfargument name="WSAJPct" type="numeric" required="yes">
		<cfargument name="PACPct" type="numeric" required="yes">
		<cfargument name="ActionPct" type="numeric" required="yes">
		<cfargument name="effectiveDate" type="string" required="yes">

		<cfset qryInsertProgramSplit = "">

		<cfquery name="qryInsertProgramSplit" datasource="#application.dsn.customapps.dsn#">
			DECLARE @splitID int;

			INSERT INTO dbo.WA_EagleProgramSplits (groupID, GLAccountID, WSAJPct, PACPct, ActionPct, effectiveDate, splitOrder)
			VALUES ( <cfqueryparam value="#arguments.groupID#" cfsqltype="CF_SQL_INTEGER">,
					 <cfqueryparam value="#arguments.glAccountID#" cfsqltype="CF_SQL_INTEGER">,
					 <cfqueryparam value="#arguments.WSAJPct#" cfsqltype="CF_SQL_INTEGER">,
					 <cfqueryparam value="#arguments.PACPct#" cfsqltype="CF_SQL_INTEGER">,
					 <cfqueryparam value="#arguments.ActionPct#" cfsqltype="CF_SQL_INTEGER">,
					 <cfqueryparam value="#arguments.effectiveDate#" cfsqltype="CF_SQL_DATE">,
					 9999 );

			SET @splitID = SCOPE_IDENTITY();

			SELECT @splitID AS splitID;
		</cfquery>

		<cfset reorderProgramSplit()>

		<cfreturn qryInsertProgramSplit.splitID>
	</cffunction>

	<cffunction name="updateProgramSplit" access="private" output="false" returntype="void">
		<cfargument name="splitID" type="numeric" required="yes">
		<cfargument name="groupID" type="numeric" required="yes">
		<cfargument name="glAccountID" type="numeric" required="yes">
		<cfargument name="WSAJPct" type="numeric" required="yes">
		<cfargument name="PACPct" type="numeric" required="yes">
		<cfargument name="ActionPct" type="numeric" required="yes">
		<cfargument name="effectiveDate" type="string" required="yes">

		<cfset qryUpdateProgramSplit = "">

		<cfquery name="qryUpdateProgramSplit" datasource="#application.dsn.customapps.dsn#">
			UPDATE dbo.WA_EagleProgramSplits
			SET groupID = <cfqueryparam value="#arguments.groupID#" cfsqltype="CF_SQL_INTEGER">,
				GLAccountID = <cfqueryparam value="#arguments.glAccountID#" cfsqltype="CF_SQL_INTEGER">,
				WSAJPct = <cfqueryparam value="#arguments.WSAJPct#" cfsqltype="CF_SQL_INTEGER">,
				PACPct = <cfqueryparam value="#arguments.PACPct#" cfsqltype="CF_SQL_INTEGER">,
				ActionPct = <cfqueryparam value="#arguments.ActionPct#" cfsqltype="CF_SQL_INTEGER">,
				effectiveDate = <cfqueryparam value="#arguments.effectiveDate#" cfsqltype="CF_SQL_DATE">
			WHERE splitID = <cfqueryparam value="#arguments.splitID#" cfsqltype="CF_SQL_INTEGER">;
		</cfquery>
	</cffunction>

	<cffunction name="reorderProgramSplit" access="private" output="false" returntype="void">
		<cfset qryReorderProgramSplit = "">

		<cfquery name="qryReorderProgramSplit" datasource="#application.dsn.customapps.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				DECLARE @tmp TABLE (neworder int NOT NULL, splitID int NOT NULL, splitOrder int NOT NULL);

				INSERT INTO @tmp (splitID, splitOrder, newOrder)
				SELECT splitID, splitOrder, ROW_NUMBER() OVER(ORDER BY splitOrder) as newOrder
				FROM dbo.WA_EagleProgramSplits;

				UPDATE eps
				SET eps.splitOrder = t.neworder
				FROM dbo.WA_EagleProgramSplits as eps 
				INNER JOIN @tmp as t on eps.splitID = t.splitID;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	</cffunction>

	<cffunction name="doProgramSplitMove" access="public" output="false" returntype="struct">
		<cfargument name="splitID" type="numeric" required="true">
		<cfargument name="dir" type="string" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfquery datasource="#application.dsn.customapps.dsn#" name="local.qryProgramSplitUp">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @splitOrder int, @splitID int;
					SET @splitID = <cfqueryparam value="#arguments.splitID#" cfsqltype="CF_SQL_INTEGER">;

					SELECT @splitOrder = splitOrder
					FROM dbo.WA_EagleProgramSplits
					WHERE splitID = @splitID;

					BEGIN TRAN;
						<cfif arguments.dir eq "up">
							UPDATE dbo.WA_EagleProgramSplits
							SET splitOrder = splitOrder + 1
							WHERE splitOrder >= @splitOrder - 1;
						
							UPDATE dbo.WA_EagleProgramSplits
							SET splitOrder = splitOrder - 2
							WHERE splitID = @splitID;
						<cfelse>
							UPDATE dbo.WA_EagleProgramSplits
							SET splitOrder = splitOrder - 1
							WHERE splitOrder <= @splitOrder + 1;

							UPDATE dbo.WA_EagleProgramSplits
							SET splitOrder = splitOrder + 2
							WHERE splitID = @splitID;
						</cfif>
					COMMIT TRAN;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset reorderProgramSplit()>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="deleteProgramSplit" access="public" output="false" returntype="struct">
		<cfargument name="splitID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfquery datasource="#application.dsn.customapps.dsn#" name="local.qryDeleteProgramSplit">
				DELETE FROM dbo.WA_EagleProgramSplits
				WHERE splitID = <cfqueryparam value="#arguments.splitID#" cfsqltype="CF_SQL_INTEGER">
			</cfquery>

			<cfset reorderProgramSplit()>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

</cfcomponent>